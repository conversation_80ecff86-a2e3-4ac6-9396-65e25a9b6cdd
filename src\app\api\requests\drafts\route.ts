import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// واجهة بيانات المسودة
interface DraftData {
  form_type: 'enhanced_improvement' | 'suggestion' | 'quick_win'
  form_data: any
  user_id: string
  draft_name?: string
}

// إنشاء client للمسودات مع تجاوز RLS
function createDraftClient() {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!url || !key) {
    throw new Error('Missing Supabase environment variables')
  }

  return createClient(url, key, {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  })
}

// POST - حفظ مسودة جديدة أو تحديث موجودة
export async function POST(request: NextRequest) {
  try {
    const body: DraftData = await request.json()

    // التحقق من صحة البيانات
    if (!body.form_type || !body.form_data || !body.user_id) {
      return NextResponse.json(
        { error: 'بيانات غير مكتملة' },
        { status: 400 }
      )
    }

    // استخدام client خاص للمسودات
    const draftClient = createDraftClient()

    // البحث عن مسودة موجودة للمستخدم من نفس النوع
    const { data: existingDraft } = await draftClient
      .from('project_requests')
      .select('id')
      .eq('requester_id', body.user_id)
      .eq('sub_type', body.form_type)
      .eq('status', 'draft')
      .single()

    // إعداد بيانات المسودة
    const departmentId = await extractDepartmentId(body.form_data)

    const draftData = {
      title: body.form_data.projectTitle || 'مسودة بدون عنوان',
      description: body.form_data.problemDescription || 'مسودة',
      main_type: 'improvement_project',
      sub_type: body.form_type,
      status: 'draft',
      priority: 'medium',
      requester_id: body.user_id,
      department_id: departmentId,
      form_data: body.form_data,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    let result
    if (existingDraft) {
      // تحديث المسودة الموجودة
      const { data, error } = await draftClient
        .from('project_requests')
        .update(draftData)
        .eq('id', existingDraft.id)
        .select()
        .single()

      if (error) {
        console.error('Database error:', error)
        return NextResponse.json(
          { error: 'حدث خطأ في تحديث المسودة' },
          { status: 500 }
        )
      }
      result = data
    } else {
      // إنشاء مسودة جديدة
      const { data, error } = await draftClient
        .from('project_requests')
        .insert([draftData])
        .select()
        .single()

      if (error) {
        console.error('Database error:', error)
        return NextResponse.json(
          { error: 'حدث خطأ في حفظ المسودة' },
          { status: 500 }
        )
      }
      result = data
    }

    return NextResponse.json({
      success: true,
      message: 'تم حفظ المسودة بنجاح',
      data: result
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// GET - جلب المسودات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const formType = searchParams.get('form_type')

    if (!userId) {
      return NextResponse.json(
        { error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }

    const draftClient = createDraftClient()

    let query = draftClient
      .from('project_requests')
      .select('*')
      .eq('requester_id', userId)
      .eq('status', 'draft')

    if (formType) {
      query = query.eq('sub_type', formType)
    }

    const { data, error } = await query.order('updated_at', { ascending: false })

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'حدث خطأ في جلب المسودات' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: data || []
    })

  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    )
  }
}

// دالة مساعدة لاستخراج معرف القسم
async function extractDepartmentId(formData: any): Promise<string> {
  let departmentName = null

  if (formData.responsibleDepartment) {
    departmentName = formData.responsibleDepartment
  } else if (formData.section) {
    departmentName = formData.section
  } else if (formData.department_id) {
    return formData.department_id
  }

  if (departmentName && typeof departmentName === 'string' && departmentName.trim()) {
    try {
      const draftClient = createDraftClient()
      const { data: department, error } = await draftClient
        .from('departments')
        .select('id, name')
        .eq('name', departmentName.trim())
        .single()

      if (!error && department) {
        return department.id
      }
    } catch (error) {
      console.error('Error finding department by name:', error)
    }
  }

  // استخدام معرف قسم موجود في قاعدة البيانات (مكتب إدارة المشاريع)
  return 'f7e8c6be-8ef0-4c4c-b6e2-be71bd2d22b5'
}