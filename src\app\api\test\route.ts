import { NextRequest, NextResponse } from 'next/server'

export async function GET() {
  console.log('Test API called')
  return NextResponse.json({ message: 'Test API works' })
}

export async function POST(request: NextRequest) {
  console.log('Test POST API called')
  const body = await request.json()
  console.log('Body:', body)
  return NextResponse.json({ message: 'Test POST API works', received: body })
}
